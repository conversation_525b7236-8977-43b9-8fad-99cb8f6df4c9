<?php
#region DOCS
/**
 * Dashboard de Empleados - Vista con Navegación por Pestañas
 *
 * Variables utilizadas en esta vista:
 * @var PDO $conexion - Conexión a la base de datos
 * @var Usuario $logged_usuario_info - Información del usuario logueado
 * @var CentroCosto $selected_centro_costo - Centro de costo seleccionado
 * @var string $fecha_inicio - Fecha de inicio para filtros (formato yyyy-mm-dd)
 * @var string $fecha_fin - Fecha de fin para filtros (formato yyyy-mm-dd)
 * @var string $success_text - Mensaje de éxito para mostrar
 * @var string $success_display - Estado de visualización del mensaje de éxito
 * @var string $error_text - Mensaje de error para mostrar
 * @var string $error_display - Estado de visualización del mensaje de error
 *
 * Pestañas implementadas:
 * - "Mis Citas Actuales": Filtros de búsqueda y lista de citas realizadas
 * - "Citas Programadas": Lista de citas programadas futuras del empleado
 */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Dashboard</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Estilos adicionales -->
	<link href="<?php echo RUTA ?>resources/css/dashboard_style.css" rel="stylesheet" />
	<link href="<?php echo RUTA ?>resources/css/dashboard-empleados-tabs.css" rel="stylesheet" />

	<!-- Include Bootstrap Datepicker CSS -->
	<link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">

				<?php #region PAGE HEADER ?>
				<div class="d-flex align-items-center mb-3">
					<div>
						<h4 class="mb-0">Dashboard</h4>
						<p class="mb-0 text-muted">Panel de control para empleados - Consulta y seguimiento de citas asignadas</p>
					</div>
				</div>

				<hr>
				<?php #endregion PAGE HEADER ?>

				<?php #region TABS NAVIGATION ?>
				<!-- Tabs Navigation -->
				<ul class="nav nav-tabs nav-tabs-v2" id="dashboard-tabs" role="tablist">
					<li class="nav-item me-2" role="presentation">
						<a href="#citas-actuales-tab-pane" class="nav-link active" id="citas-actuales-tab" data-bs-toggle="tab" role="tab" aria-controls="citas-actuales-tab-pane" aria-selected="true">
							<i class="fa fa-calendar-check fa-fw me-1"></i> Mis Citas Actuales
						</a>
					</li>
					<li class="nav-item" role="presentation">
						<a href="#citas-programadas-tab-pane" class="nav-link" id="citas-programadas-tab" data-bs-toggle="tab" role="tab" aria-controls="citas-programadas-tab-pane" aria-selected="false">
							<i class="fa fa-calendar-plus fa-fw me-1"></i> Citas Programadas
						</a>
					</li>
				</ul>
				<?php #endregion TABS NAVIGATION ?>

				<!-- Tabs Content -->
				<div class="tab-content pt-3" id="dashboard-tabs-content">
					<!-- Mis Citas Actuales Tab -->
					<div class="tab-pane fade show active" id="citas-actuales-tab-pane" role="tabpanel" aria-labelledby="citas-actuales-tab">

				<!-- Date Filter Panel -->
				<div class="panel panel-inverse mb-3">
					<div class="panel-heading">
						<h4 class="panel-title">Filtros de Búsqueda</h4>
					</div>
					<div class="panel-body">
						<form id="consulta-form">
							<div class="row g-3">
								<!-- Fecha Inicio -->
								<div class="col-md-6">
									<label for="fecha_inicio" class="form-label">Fecha Inicio: <span class="text-danger">*</span></label>
									<div class="input-group">
										<input type="text" class="form-control datepicker" id="fecha_inicio" name="fecha_inicio"
											   placeholder="yyyy-mm-dd" value="<?= htmlspecialchars($fecha_inicio) ?>" required>
										<span class="input-group-text" id="fecha_inicio_icon" style="cursor: pointer;">
											<i class="fa fa-calendar"></i>
										</span>
									</div>
								</div>

								<!-- Fecha Fin -->
								<div class="col-md-6">
									<label for="fecha_fin" class="form-label">Fecha Fin: <span class="text-danger">*</span></label>
									<div class="input-group">
										<input type="text" class="form-control datepicker" id="fecha_fin" name="fecha_fin"
											   placeholder="yyyy-mm-dd" value="<?= htmlspecialchars($fecha_fin) ?>" required>
										<span class="input-group-text" id="fecha_fin_icon" style="cursor: pointer;">
											<i class="fa fa-calendar"></i>
										</span>
									</div>
								</div>
							</div>

							<div class="row g-3 mt-1 justify-content-end">
								<!-- Search Button -->
								<div class="col-md-auto">
									<button type="submit" class="btn btn-primary">
										<i class="fa fa-search fa-fw me-1"></i> Consultar Mis Citas
									</button>
								</div>
								<!-- Clear Filters Button -->
								<div class="col-md-auto">
									<button type="button" class="btn btn-secondary" id="clear-filters-btn">
										<i class="fa fa-times fa-fw me-1"></i> Limpiar
									</button>
								</div>
							</div>
						</form>
					</div>
				</div>

				<!-- Results Panel -->
				<div class="panel panel-inverse">
					<div class="panel-heading">
						<h4 class="panel-title">Mis Citas</h4>
					</div>
					<div>
						<!-- Empty state -->
						<div id="no-results" class="text-center py-4">
							<i class="fa fa-search fa-3x text-muted mb-3"></i>
							<h5 class="text-muted">Utilice los filtros para consultar sus citas</h5>
							<p class="text-muted">Seleccione un rango de fechas para ver sus citas asignadas.</p>
						</div>

						<!-- Loading indicator -->
						<div id="loading" class="text-center py-4" style="display: none;">
							<i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
							<h5 class="text-primary">Consultando registros...</h5>
						</div>

						<!-- Results table -->
						<div id="results-table-container" style="display: none;">
							<div class="table-responsive">
								<table class="table table-hover table-sm mb-0" id="citas-table">
									<thead class="table-dark">
										<tr>
											<th class="text-center" style="width: 100px;">Acciones</th>
											<th>Centro de costo</th>
											<th>Puesto</th>
											<th>Método Pago</th>
											<th class="text-center" style="width: 140px;">Fecha Inicio</th>
											<th class="text-center" style="width: 140px;">Fecha Fin</th>
											<th class="text-center" style="width: 100px;">Estado</th>
											<th class="text-end" style="width: 120px;">Total</th>
											<th class="text-end" style="width: 120px;"><span class="text-success">Comisión</span></th>
										</tr>
									</thead>
									<tbody id="citas-table-body">
										<!-- Results will be populated via AJAX -->
									</tbody>
									<tfoot id="citas-table-footer" style="display: none;">
										<tr class="table-info">
											<th colspan="7" class="text-end fw-bold">Total:</th>
											<th class="text-end fw-bold" id="citas-total-valor">$0</th>
											<th class="text-end fw-bold" id="citas-total-comision">$0</th>
										</tr>
									</tfoot>
								</table>
							</div>
						</div>
					</div>

					</div>
					<!-- END Mis Citas Actuales Tab -->

					<!-- Citas Programadas Tab -->
					<div class="tab-pane fade" id="citas-programadas-tab-pane" role="tabpanel" aria-labelledby="citas-programadas-tab">

						<!-- Citas Programadas Panel -->
						<div class="panel panel-inverse">
							<div class="panel-heading">
								<h4 class="panel-title">Citas Programadas</h4>
								<div class="panel-heading-btn">
									<button type="button" class="btn btn-sm btn-primary" id="refresh-citas-programadas-btn">
										<i class="fa fa-refresh me-1"></i> Actualizar
									</button>
								</div>
							</div>
							<div>
								<!-- Loading indicator -->
								<div id="citas-programadas-loading" class="citas-programadas-loading" style="display: none;">
									<i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
									<h5 class="text-primary">Cargando citas programadas...</h5>
								</div>

								<!-- Empty state -->
								<div id="citas-programadas-empty" class="citas-programadas-empty">
									<i class="fa fa-calendar-plus fa-3x text-muted mb-3"></i>
									<h5 class="text-muted">No hay citas programadas</h5>
									<p class="text-muted">No tienes citas programadas próximas en este centro de costo.</p>
								</div>

								<!-- Results table -->
								<div id="citas-programadas-table-container" style="display: none;">
									<div class="table-responsive">
										<table class="table table-hover table-sm mb-0 citas-programadas-table" id="citas-programadas-table">
											<thead class="table-dark">
												<tr>
													<th class="text-center" style="width: 100px;">Acciones</th>
													<th>Cliente</th>
													<th>Empleado</th>
													<th class="text-center" style="width: 140px;">Fecha/Hora</th>
													<th class="text-center" style="width: 100px;">Duración</th>
													<th class="text-center" style="width: 100px;">Estado</th>
												</tr>
											</thead>
											<tbody id="citas-programadas-table-body">
												<!-- Results will be populated via AJAX -->
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>

					</div>
					<!-- END Citas Programadas Tab -->

				</div>
				<!-- END Tabs Content -->

		</div>
	</div>
	<!-- END #content -->

	<?php #region View Services Modal ?>
	<div class="modal fade" id="viewServicesModal" tabindex="-1" aria-labelledby="viewServicesModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="viewServicesModalLabel">Servicios de la Cita</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<!-- Loading indicator -->
					<div id="services-modal-loading" class="text-center py-4">
						<i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
						<h5 class="text-primary">Cargando servicios...</h5>
					</div>

					<!-- Services content -->
					<div id="services-modal-content" style="display: none;">
						<!-- Cita Header Information -->
						<div class="panel panel-inverse mb-3">
							<div class="panel-heading">
								<h4 class="panel-title">Información de la Cita</h4>
							</div>
							<div class="panel-body">
								<div class="row">
									<div class="col-md-6">
										<div class="mb-3">
											<label class="form-label fw-bold text-primary">Puesto:</label>
											<div id="service-detail-puesto" class="form-control-plaintext text-white fs-6"></div>
										</div>
										<div class="mb-3">
											<label class="form-label fw-bold text-primary">Empleado:</label>
											<div id="service-detail-empleado" class="form-control-plaintext text-white fs-6"></div>
										</div>
									</div>
									<div class="col-md-6">
										<div class="mb-3">
											<label class="form-label fw-bold text-primary">Fecha Inicio:</label>
											<div id="service-detail-fecha-inicio" class="form-control-plaintext text-white fs-6"></div>
										</div>
										<div class="mb-3">
											<label class="form-label fw-bold text-primary">Fecha Fin:</label>
											<div id="service-detail-fecha-fin" class="form-control-plaintext text-white fs-6"></div>
										</div>
										<div class="mb-3">
											<label class="form-label fw-bold text-primary">Método de Pago:</label>
											<div id="service-detail-metodo-pago" class="form-control-plaintext text-white fs-6"></div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Services Table -->
						<div class="panel panel-inverse">
							<div class="panel-heading">
								<h4 class="panel-title">Servicios Realizados</h4>
							</div>
							<div>
								<div class="table-responsive">
									<table class="table table-hover table-sm mb-0" id="services-table">
										<thead class="table-dark">
											<tr>
												<th>Servicio</th>
												<th class="text-end" style="width: 120px;">Valor</th>
											</tr>
										</thead>
										<tbody id="services-table-body">
											<!-- Services will be populated via AJAX -->
										</tbody>
										<tfoot>
											<tr class="table-info">
												<th class="text-end fw-bold">Total:</th>
												<th class="text-end fw-bold" id="services-total-valor"></th>
											</tr>
										</tfoot>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
				</div>
			</div>
		</div>
	</div>
	<?php #endregion View Services Modal ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datepickers
    $('.datepicker').datepicker({
        autoclose     : true,
        todayHighlight: true,
        format        : 'yyyy-mm-dd'
    });

    // Make calendar icons clickable
    document.getElementById('fecha_inicio_icon').addEventListener('click', function() {
        $('#fecha_inicio').datepicker('show');
    });

    document.getElementById('fecha_fin_icon').addEventListener('click', function() {
        $('#fecha_fin').datepicker('show');
    });

    // Form Elements
    const consultaForm          = document.getElementById('consulta-form');
    const noResults             = document.getElementById('no-results');
    const loading               = document.getElementById('loading');
    const resultsTableContainer = document.getElementById('results-table-container');
    const citasTableBody        = document.getElementById('citas-table-body');
    const clearFiltersBtn       = document.getElementById('clear-filters-btn');

    // Form validation
    consultaForm.addEventListener('submit', function(e) {
        const fechaInicio = document.getElementById('fecha_inicio').value;
        const fechaFin    = document.getElementById('fecha_fin').value;

        if (!fechaInicio || !fechaFin) {
            e.preventDefault();
            showSweetAlertError('Error de Validación', 'Las fechas de inicio y fin son obligatorias.');
            return false;
        }

        if (new Date(fechaInicio) > new Date(fechaFin)) {
            e.preventDefault();
            showSweetAlertError('Error de Validación', 'La fecha de inicio no puede ser mayor que la fecha de fin.');
            return false;
        }
    });

    // Search form submission
    consultaForm.addEventListener('submit', function(e) {
        e.preventDefault();
        searchCitas();
    });

    // Clear filters functionality
    clearFiltersBtn.addEventListener('click', function() {
        // Clear date fields
        document.getElementById('fecha_inicio').value = '';
        document.getElementById('fecha_fin').value = '';

        // Clear results and show empty state
        noResults.innerHTML = `
            <i class="fa fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Utilice los filtros para consultar sus citas</h5>
            <p class="text-muted">Seleccione un rango de fechas para ver sus citas asignadas.</p>
        `;
        noResults.style.display             = 'block';
        resultsTableContainer.style.display = 'none';
        loading.style.display               = 'none';

        // Hide table footer when clearing filters
        const citasTableFooter = document.getElementById('citas-table-footer');
        citasTableFooter.style.display = 'none';
    });

    // Search function
    function searchCitas() {
        const formData = new FormData(consultaForm);
        formData.append('action', 'search_citas_empleado');

        // Show loading
        noResults.style.display             = 'none';
        resultsTableContainer.style.display = 'none';
        loading.style.display               = 'block';

        fetch('dashboard-empleados', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';

            if (data.success) {
                if (data.citas.length > 0) {
                    displayResults(data.citas);
                } else {
                    showNoResults();
                }
            } else {
                showSweetAlertError('Error', data.message || 'Error al consultar las citas');
                showNoResults();
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            showSweetAlertError('Error de Conexión', 'Error de conexión al consultar las citas');
            showNoResults();
            console.error('Error:', error);
        });
    }

    // Display results in table
    function displayResults(citas) {
        citasTableBody.innerHTML = '';
        let totalGeneral = 0;
        let totalComision = 0;

        citas.forEach(cita => {
            const row = document.createElement('tr');

            // Determinar el badge y texto del estado
            let estadoHtml;
            if (cita.estado === 1) {
                estadoHtml = `<span class="badge bg-success">${cita.estado_texto}</span>`;
            } else {
                estadoHtml = `<span class="badge bg-danger">${cita.estado_texto}</span>`;
                if (cita.razon_cancelacion) {
                    estadoHtml += `<br><small class="text-muted">(${cita.razon_cancelacion})</small>`;
                }
            }

            // Calcular totales
            const valorNumerico = parseFloat(cita.total_valor_servicios) || 0;
            const comisionNumerica = parseFloat(cita.valor_comision_empleado) || 0;
            totalGeneral += valorNumerico;
            totalComision += comisionNumerica;

            // Format commission value
            const comisionFormateada = new Intl.NumberFormat('es-CO', {
                style: 'currency',
                currency: 'COP',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(comisionNumerica);

            row.innerHTML = `
                <td class="text-center align-middle">
                    <button class="btn btn-info btn-xs" onclick="verServiciosCita(${cita.id})" title="Ver servicios de la cita">
                        <i class="fa fa-eye"></i>
                    </button>
                </td>
                <td class="align-middle">${cita.centro_costo_nombre || 'N/A'}</td>
                <td class="align-middle">${cita.descripcion_puesto || 'N/A'}</td>
                <td class="align-middle">${cita.nombre_metodo_pago || 'N/A'}</td>
                <td class="text-center align-middle">${cita.fecha_inicio_formateada}</td>
                <td class="text-center align-middle">${cita.fecha_fin_formateada}</td>
                <td class="text-center align-middle">${estadoHtml}</td>
                <td class="text-end align-middle">${cita.total_valor_formateado}</td>
                <td class="text-end align-middle text-success">${comisionFormateada}</td>
            `;

            citasTableBody.appendChild(row);
        });

        // Show table and update footer
        resultsTableContainer.style.display = 'block';
        noResults.style.display = 'none';

        // Update footer totals
        const citasTableFooter = document.getElementById('citas-table-footer');
        const citasTotalValor = document.getElementById('citas-total-valor');
        const citasTotalComision = document.getElementById('citas-total-comision');

        const totalFormateado = new Intl.NumberFormat('es-CO', {
            style: 'currency',
            currency: 'COP',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(totalGeneral);

        const totalComisionFormateado = new Intl.NumberFormat('es-CO', {
            style: 'currency',
            currency: 'COP',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(totalComision);

        citasTotalValor.textContent = totalFormateado;
        citasTotalComision.textContent = totalComisionFormateado;
        citasTableFooter.style.display = 'table-footer-group';
    }

    // Show no results message
    function showNoResults() {
        noResults.innerHTML = `
            <i class="fa fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No se encontraron citas</h5>
            <p class="text-muted">No hay citas registradas para el rango de fechas seleccionado.</p>
        `;
        noResults.style.display = 'block';
        resultsTableContainer.style.display = 'none';

        // Hide table footer
        const citasTableFooter = document.getElementById('citas-table-footer');
        citasTableFooter.style.display = 'none';
    }

    console.log('Dashboard de empleados cargado correctamente');
});

// Global function for viewing cita services
window.verServiciosCita = function(citaId) {
    const modal        = new bootstrap.Modal(document.getElementById('viewServicesModal'));
    const modalLoading = document.getElementById('services-modal-loading');
    const modalContent = document.getElementById('services-modal-content');

    // Show modal and loading state
    modal.show();
    modalLoading.style.display = 'block';
    modalContent.style.display = 'none';

    // Fetch cita services
    const formData = new FormData();
    formData.append('action', 'get_cita_services');
    formData.append('cita_id', citaId);

    fetch('dashboard-empleados', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        modalLoading.style.display = 'none';

        if (data.success) {
            populateCitaServices(data.cita, data.servicios, data.total_valor_formateado);
            modalContent.style.display = 'block';
        } else {
            showSweetAlertError('Error', data.message || 'Error al cargar los servicios de la cita');
            modal.hide();
        }
    })
    .catch(error => {
        modalLoading.style.display = 'none';
        showSweetAlertError('Error de Conexión', 'Error de conexión al cargar los servicios de la cita');
        modal.hide();
        console.error('Error:', error);
    });
};

// Function to populate cita services in modal
function populateCitaServices(cita, servicios, totalFormateado) {
    // Populate header information
    document.getElementById('service-detail-puesto').textContent = cita.descripcion_puesto || 'N/A';
    document.getElementById('service-detail-empleado').textContent = cita.nombre_empleado || 'N/A';
    document.getElementById('service-detail-metodo-pago').textContent = cita.nombre_metodo_pago || 'N/A';

    // Format dates for display
    const fechaInicio = cita.fecha_inicio ? formatearFechaHoraJS(cita.fecha_inicio) : 'N/A';
    const fechaFin    = cita.fecha_fin ? formatearFechaHoraJS(cita.fecha_fin) : 'N/A';

    document.getElementById('service-detail-fecha-inicio').textContent = fechaInicio;
    document.getElementById('service-detail-fecha-fin').textContent = fechaFin;

    // Populate services table
    const servicesTableBody = document.getElementById('services-table-body');
    servicesTableBody.innerHTML = '';

    if (servicios.length > 0) {
        servicios.forEach(servicio => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="align-middle">${servicio.descripcion}</td>
                <td class="text-end align-middle">${servicio.valor_formateado}</td>
            `;
            servicesTableBody.appendChild(row);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="2" class="text-center text-muted py-3">
                <i class="fa fa-info-circle me-2"></i>No hay servicios registrados para esta cita
            </td>
        `;
        servicesTableBody.appendChild(row);
    }

    // Set total
    document.getElementById('services-total-valor').textContent = totalFormateado;
}

// Helper function to format date and time in JavaScript
function formatearFechaHoraJS(fechaHora) {
    if (!fechaHora) return 'N/A';

    try {
        const fecha   = new Date(fechaHora);
        const year    = fecha.getFullYear();
        const month   = String(fecha.getMonth() + 1).padStart(2, '0');
        const day     = String(fecha.getDate()).padStart(2, '0');
        let   hours   = fecha.getHours();
        const minutes = String(fecha.getMinutes()).padStart(2, '0');
        const ampm    = hours >= 12 ? 'PM' : 'AM';
              hours   = hours % 12;
              hours   = hours ? hours : 12;                             // the hour '0' should be '12'

        return `${year}-${month}-${day} ${hours}:${minutes} ${ampm}`;
    } catch (e) {
        return fechaHora;
    }
}
</script>

<?php #endregion JS ?>
</body>
</html>
